import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { ReviewService } from "@/app/api/services/review.service";
import { verifyAdminToken } from "@/lib/admin/auth";

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const rating = searchParams.get("rating") || "";

    // Get reviews with pagination using repository directly
    const reviewRepository = container.resolve<any>("ReviewRepository");
    const result = await reviewRepository.searchReviews({
      page,
      limit,
      search: search || undefined,
      status: status && status !== "all" ? (status as any) : undefined,
      rating: rating && rating !== "all" ? parseInt(rating) : undefined,
    });

    return NextResponse.json({
      data: result.data,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit),
        hasNext: page < Math.ceil(result.total / limit),
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const reviewId = searchParams.get("id");

    if (!reviewId) {
      return NextResponse.json(
        { error: "Review ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status } = body;

    if (!["PENDING", "APPROVED", "REJECTED"].includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 });
    }

    // Update review status
    const reviewService = container.resolve<ReviewService>(SERVICE_IDENTIFIERS.REVIEW_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Update review status
    const updatedReview = await reviewService.updateReview(
      reviewId,
      { status: status as any },
      adminUserEntity
    );

    // If approved, update product rating
    if (status === "APPROVED") {
      await updateProductRating(updatedReview.productId);
    }

    return NextResponse.json({
      data: updatedReview,
      message: `Review ${status.toLowerCase()} successfully`,
    });
  } catch (error) {
    console.error("Error updating review:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to update product rating
async function updateProductRating(productId: string) {
  try {
    const productRepository = container.resolve<any>("ProductRepository");

    // Update product rating using repository
    await productRepository.updateRating(productId);
  } catch (error) {
    console.error("Error updating product rating:", error);
  }
}
